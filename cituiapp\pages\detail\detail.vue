<template>
	<view class="page-container">
		<!-- 固定顶部导航 -->
		<view class="fixed-header">
			<view class="header-content">
				<view class="back-btn" @click="goBack">
					<text class="back-icon">‹</text>
				</view>
				<view class="header-title">详情</view>
				<view class="header-placeholder"></view>
			</view>
		</view>
		
		<!-- 页面内容区域 -->
		<view class="content-container">
			<scroll-view 
				class="scroll-container"
				scroll-y
				refresher-enabled
				:refresher-triggered="isRefreshing"
				@refresherrefresh="onRefresh"
				@refresherrestore="onRestore"
			>
				<view class="content-inner">
					<!-- 头部信息 -->
					<view class="app-info-section">
						<view class="app-info-card">
							<image
								:src="appInfo.logo || appInfo.logoUrl || 'https://placehold.co/200x200'"
								class="app-logo"
								mode="aspectFill"
								@click="previewImage(appInfo.logo || appInfo.logoUrl || 'https://placehold.co/200x200')"
							></image>
							<view class="app-details">
								<view class="app-header">
									<text class="app-name">{{ appInfo.name }}</text>
									<view class="download-count">
										<text class="download-icon">⬇</text>
										<text class="count-text">{{ appInfo.downloadCount }}次</text>
									</view>
								</view>
								<view class="app-meta">
									<view class="rating-info">
										<text class="star-icon">⭐</text>
										<text class="rating-text">{{ appInfo.rating }}分（{{ appInfo.type }}）</text>
									</view>
								</view>
								<view class="tags-container">
									<view 
										v-for="tag in appInfo.tags" 
										:key="tag.text"
										class="tag"
										:class="getTagClass(tag.type)"
									>
										<text class="tag-text">{{ tag.emoji }}{{ tag.text }}</text>
									</view>
								</view>
							</view>
						</view>
					</view>

					<!-- 数据看板 -->
					<view class="data-section">
						<view class="section-card">
							<text class="section-title">测试数据报告</text>
							
							<!-- 数据统计网格 -->
							<view class="data-grid">
								<view class="data-item">
									<text class="data-label">测试条数</text>
									<text class="data-value">{{ testData.testCount }}条</text>
								</view>
								<view class="data-item">
									<text class="data-label">测试收益</text>
									<text class="data-value">¥{{ testData.testEarnings }}</text>
								</view>
								<view class="data-item">
									<text class="data-label">测试时长</text>
									<text class="data-value">{{ testData.testDuration }}分钟</text>
								</view>
								<view class="data-item">
									<text class="data-label">测试设备</text>
									<text class="data-value">{{ testData.testDevice }}</text>
								</view>
							</view>
							
							<!-- 购买同款设备按钮 -->
							<!-- <view class="device-btn" @click="buyDevice">
								<text class="device-btn-text">购买同款设备</text>
							</view> -->
							
							<!-- 三重验证真机实测 -->
							<view class="verification-section">
								<text class="verification-title">三重验证真机实测</text>
								<view class="verification-grid">
									<view 
										v-for="(item, index) in verificationImages" 
										:key="index"
										class="verification-item"
									>
										<image
											:src="item.image"
											class="verification-image"
											mode="aspectFill"
											@click="previewVerificationImages(index)"
										></image>
										<text class="verification-label">{{ item.label }}</text>
									</view>
								</view>
							</view>
						</view>
					</view>

					<!-- 测评报告 -->
					<view class="report-section">
						<view class="section-card">
							<view class="report-header">
								<text class="lock-icon">🔒</text>
								<text class="section-title">测评报告</text>
							</view>
							<view class="report-meta">
								<text class="report-time">评测时间：{{ reportInfo.evaluationTime }}</text>
								<text class="report-author">评测人：{{ reportInfo.evaluator }}</text>
							</view>
							<view class="report-content">
								<text class="report-text">{{ reportInfo.content }}</text>
							</view>
						</view>
					</view>

					<!-- 放水记录 -->
					<view class="water-section">
						<view class="section-card">
							<view class="water-header">
								<view class="water-title-group">
									<text class="water-icon">💧</text>
									<text class="section-title">放水记录</text>
								</view>
								<!-- <view class="submit-clue-btn" @click="submitClue">
									<text class="plus-icon">+</text>
									<text class="submit-text">提交放水线索赚积分</text>
								</view> -->
							</view>
							
							<!-- 放水记录列表 -->
							<view class="water-records">
								<view 
									v-for="(record, index) in waterRecords" 
									:key="record.id"
									class="water-record-item"
									:class="{ 'border-bottom': index < waterRecords.length - 1 }"
								>
									<view class="user-avatar">
										<image 
											:src="record.avatar" 
											class="avatar-image"
											mode="aspectFill"
										></image>
									</view>
									<view class="record-content">
										<view class="record-header">
											<view class="user-info">
												<text class="user-name">{{ record.userName }}</text>
												<text 
													class="verify-status"
													:class="record.isVerified ? 'verified' : 'unverified'"
												>
												</text>
											</view>
											<view 
												class="amount-tag"
												:class="getAmountTagClass(record.amount)"
											>
												<text class="amount-text">放水{{ record.amount }}元</text>
											</view>
										</view>
										<text class="record-description">{{ record.description }}</text>
										<view class="record-footer">
											<text class="device-info">{{ record.device }}</text>
											<text class="record-time">{{ record.time }}</text>
										</view>
										<!-- 截图展示 -->
										<view v-if="record.screenshots && record.screenshots.length > 0" class="screenshots">
											<image
												v-for="(screenshot, idx) in record.screenshots"
												:key="idx"
												:src="screenshot"
												class="screenshot-image"
												mode="aspectFill"
												@click="previewRecordImages(record.screenshots, idx)"
											></image>
										</view>
									</view>
								</view>
							</view>
							
							<!-- 查看更多记录按钮 -->
							<view class="load-more-container" v-if="showLoadMore">
								<view 
									class="load-more-btn" 
									@click="loadMoreRecords"
									:class="{ 'loading': isLoading }"
								>
									<text class="load-more-text">
										{{ isLoading ? '加载中...' : hasMoreData ? '查看更多记录' : '没有更多记录了' }}
									</text>
									<text v-if="!isLoading && hasMoreData" class="chevron-icon">›</text>
								</view>
							</view>
						</view>
					</view>

					<!-- 常见问题 -->
					<!-- <view class="faq-section">
						<view class="section-card">
							<view class="faq-header">
								<text class="help-icon">❓</text>
								<text class="section-title">常见问题</text>
							</view>
							<view class="faq-list">
								<view 
									v-for="(faq, index) in faqList" 
									:key="index"
									class="faq-item"
									:class="{ 'border-bottom': index < faqList.length - 1 }"
								>
									<text class="faq-question">{{ faq.question }}</text>
									<text class="faq-answer">{{ faq.answer }}</text>
								</view>
							</view>
							<view class="more-faq-btn" @click="viewMoreFAQ">
								<text class="more-faq-text">更多问题解答</text>
								<text class="chevron-icon">›</text>
							</view>
						</view>
					</view> -->
					
					<!-- 底部安全区域 -->
					<view class="safe-area-bottom"></view>
				</view>
			</scroll-view>
		</view>
		
		<!-- 底部行动按钮 -->
		<view class="action-button-container">
			<view class="action-button" @click="startEarning">
				<text class="action-button-text">立即赚钱</text>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				isRefreshing: false,
				isLoading: false,
				showLoadMore: true,
				hasMoreData: true,
				currentPage: 1,
				pageSize: 5,
				
				// APP信息
				appInfo: {
					id: 1,
					name: '金币大师',
					type: '合成小游戏',
					rating: 4.7,
					downloadCount: 3560,
					logo: 'https://placehold.co/200x200',
					tags: []
				},
				
				// 测试数据
				testData: {
					testCount: 0,
					testEarnings: 0,
					testDuration: 0,
					testDevice: ''
				},
				
				// 验证图片
				verificationImages: [],
				
				// 报告信息
				reportInfo: {
					evaluationTime: '',
					evaluator: '',
					content: ''
				},
				
				// 放水记录
				waterRecords: [],
				
				// 常见问题
				faqList: [
					{
						question: '如何让红包变大？',
						answer: '连续签到7天可获得更大红包，邀请好友也可增加红包额度。开通VIP会员后，所有红包额度提升50%。每天10-12点活动期间，红包金额翻倍。'
					},
					{
						question: '红包提现不到账如何避免？',
						answer: '建议使用实名账号，提现前完成身份验证。检查银行卡信息是否正确。部分平台首次提现可能有24小时审核期，耐心等待。若超过48小时未到账，可联系客服处理。'
					}
				]
			}
		},
		
		methods: {
			// 返回上一页
			goBack() {
				uni.navigateBack()
			},

			// 预览单张图片
			previewImage(imageUrl) {
				if (!imageUrl || imageUrl === 'https://placehold.co/200x200') {
					return
				}
				uni.previewImage({
					urls: [imageUrl],
					current: imageUrl
				})
			},

			// 预览验证图片组
			previewVerificationImages(currentIndex) {
				const imageUrls = this.verificationImages
					.filter(item => item.image && item.image !== 'https://placehold.co/200x200')
					.map(item => item.image)

				if (imageUrls.length === 0) {
					return
				}

				uni.previewImage({
					urls: imageUrls,
					current: currentIndex < imageUrls.length ? imageUrls[currentIndex] : imageUrls[0]
				})
			},

			// 预览放水记录截图
			previewRecordImages(screenshots, currentIndex) {
				if (!screenshots || screenshots.length === 0) {
					return
				}

				const validImages = screenshots.filter(img => img && img !== 'https://placehold.co/200x200')
				if (validImages.length === 0) {
					return
				}

				uni.previewImage({
					urls: validImages,
					current: currentIndex < validImages.length ? validImages[currentIndex] : validImages[0]
				})
			},
			
			// 下拉刷新
			onRefresh() {
				this.isRefreshing = true
				setTimeout(() => {
					// 重置数据并重新加载
					this.currentPage = 1
					this.hasMoreData = true
					this.loadWaterRecords(true)
					this.isRefreshing = false
				}, 1500)
			},
			
			// 刷新恢复
			onRestore() {
				this.isRefreshing = false
			},
			
			// 获取标签样式
			getTagClass(type) {
				return `tag-${type}`
			},
			
			// 获取金额标签样式
			getAmountTagClass(amount) {
				const amountNum = parseFloat(amount)
				if (amountNum >= 1.0) return 'amount-high'
				if (amountNum >= 0.5) return 'amount-medium'
				return 'amount-low'
			},
			
			// 购买同款设备
			buyDevice() {
				/* uni.showToast({
					title: '跳转到设备购买页面',
					icon: 'none'
				}) */
			},
			
			// 提交线索
			submitClue() {
				uni.showToast({
					title: '跳转到提交线索页面',
					icon: 'none'
				})
			},
			
			// 查看更多FAQ
			viewMoreFAQ() {
				uni.showToast({
					title: '查看更多问题解答',
					icon: 'none'
				})
			},
			
			// 开始赚钱
			startEarning() {
				uni.showToast({
					title: '开始赚钱',
					icon: 'success'
				})
			},

			// 加载APP详情数据
			loadAppDetail() {
				if (!this.appInfo.id) {
					uni.showToast({
						title: 'APP ID不能为空',
						icon: 'none'
					})
					return
				}

				uni.$u.http.get('/app/detail', {
					params: {
						id: this.appInfo.id
					}
				}).then(res => {
					console.log('APP详情API返回数据:', res)

					// 更新APP信息
					if (res.app_info) {
						this.appInfo = {
							...this.appInfo,
							...res.app_info
						}
					}

					// 更新测试数据
					if (res.test_data) {
						this.testData = {
							...this.testData,
							...res.test_data
						}
					}

					// 更新验证图片
					if (res.verification_images && res.verification_images.length > 0) {
						this.verificationImages = res.verification_images.filter(item => item.image)
					}

					// 更新报告信息
					if (res.report_info) {
						this.reportInfo = {
							...this.reportInfo,
							...res.report_info
						}
					}

				}).catch(err => {
					console.error('加载APP详情失败:', err)
					uni.showToast({
						title: '加载详情失败',
						icon: 'none'
					})
				})
			},
			
			// 加载放水记录
			loadWaterRecords(isRefresh = false) {
				if (!this.appInfo.id) {
					return
				}

				if (isRefresh) {
					this.waterRecords = []
					this.currentPage = 1
					this.hasMoreData = true
				}

				// 调用真实API
				uni.$u.http.get('/clue/app_clues', {
					params: {
						app_id: this.appInfo.id,
						page: this.currentPage,
						page_size: this.pageSize
					}
				}).then(res => {
					console.log('放水记录返回数据:', res)

					const newRecords = res.list || []

					if (isRefresh) {
						this.waterRecords = newRecords
					} else {
						this.waterRecords = [...this.waterRecords, ...newRecords]
					}

					// 更新分页状态
					this.hasMoreData = res.has_more || false


				}).catch(err => {
					console.error('加载放水记录失败:', err)
				})
			},
			
			// 加载更多记录
			loadMoreRecords() {
				if (this.isLoading || !this.hasMoreData) {
					return
				}
				
				this.isLoading = true
				
				setTimeout(() => {
					this.currentPage++
					this.loadWaterRecords(false)
					this.isLoading = false
				}, 1000)
			},
		},
		
		onLoad(options) {
			// 获取传递的参数
			if (options.id) {
				this.appInfo.id = options.id
			}
			if (options.name) {
				this.appInfo.name = decodeURIComponent(options.name)
			}

			// 加载APP详情数据
			this.loadAppDetail()

			// 初始加载放水记录
			this.loadWaterRecords(true)
		}
	}
</script>

<style lang="scss" scoped>
.page-container {
	width: 100%;
	height: 100vh;
	background-color: #f5f5f5;
}

/* 固定顶部导航 */
.fixed-header {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 999;
	background-color: #ffffff;
	border-bottom: 2rpx solid #f0f0f0;
	
	.header-content {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 32rpx;
		min-height: 120rpx;
		
		.back-btn {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 48rpx;
			height: 48rpx;
			
			.back-icon {
				font-size: 48rpx;
				color: #4b5563;
				font-weight: bold;
			}
		}
		
		.header-title {
			font-size: 36rpx;
			font-weight: bold;
			color: #111827;
		}
		
		.header-placeholder {
			width: 48rpx;
			height: 48rpx;
		}
	}
}

/* 内容区域 */
.content-container {
	padding-top: 120rpx;
	padding-bottom: 120rpx; /* 为底部按钮留出空间 */
	height: 100vh;
	
	.scroll-container {
		height: 100%;
		
		.content-inner {
			padding: 32rpx;
		}
	}
}

/* APP信息区域 */
.app-info-section {
	margin-bottom: 32rpx;
	
	.app-info-card {
		display: flex;
		background: #ffffff;
		border-radius: 16rpx;
		padding: 32rpx;
		box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.1);
		
		.app-logo {
			width: 112rpx;
			height: 112rpx;
			border-radius: 16rpx;
			margin-right: 24rpx;
			cursor: pointer;
			transition: transform 0.2s ease;

			&:active {
				transform: scale(0.95);
			}
		}
		
		.app-details {
			flex: 1;
			
			.app-header {
				display: flex;
				justify-content: space-between;
				align-items: flex-start;
				margin-bottom: 16rpx;
				
				.app-name {
					font-size: 36rpx;
					font-weight: bold;
					color: #111827;
				}
				
				.download-count {
					display: flex;
					align-items: center;
					color: #6b7280;
					
					.download-icon {
						font-size: 24rpx;
						margin-right: 4rpx;
					}
					
					.count-text {
						font-size: 24rpx;
					}
				}
			}
			
			.app-meta {
				margin-bottom: 16rpx;
				
				.rating-info {
					display: flex;
					align-items: center;
					
					.star-icon {
						font-size: 24rpx;
						margin-right: 4rpx;
					}
					
					.rating-text {
						font-size: 28rpx;
						color: #f59e0b;
						font-weight: 500;
					}
				}
			}
			
			.tags-container {
				display: flex;
				flex-wrap: wrap;
				gap: 12rpx;
				
				.tag {
					border-radius: 24rpx;
					padding: 6rpx 16rpx;
					display: inline-flex;
					align-items: center;
					justify-content: center;
					min-height: 44rpx;
					
					.tag-text {
						font-size: 22rpx;
						font-weight: 500;
						line-height: 1;
					}
					
					&.tag-red {
						background-color: #fee2e2;
						
						.tag-text {
							color: #dc2626;
						}
					}
					
					&.tag-amber {
						background-color: #fef3c7;
						
						.tag-text {
							color: #d97706;
						}
					}
					
					&.tag-blue {
						background-color: #dbeafe;

						.tag-text {
							color: #2563eb;
						}
					}

					&.tag-green {
						background-color: #dcfce7;

						.tag-text {
							color: #16a34a;
						}
					}

					&.tag-gray {
						background-color: #f3f4f6;

						.tag-text {
							color: #6b7280;
						}
					}
				}
			}
		}
	}
}

/* 数据区域 */
.data-section {
	margin-bottom: 32rpx;
}

/* 报告区域 */
.report-section {
	margin-bottom: 32rpx;
}

/* 放水记录区域 */
.water-section {
	margin-bottom: 32rpx;
}

/* FAQ区域 */
.faq-section {
	margin-bottom: 32rpx;
}

/* 通用卡片样式 */
.section-card {
	background: #ffffff;
	border-radius: 16rpx;
	padding: 32rpx;
	box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.1);
	
	.section-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #111827;
		margin-left: 8rpx;
	}
}

/* 数据网格 */
.data-grid {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 16rpx;
	margin: 24rpx 0;
	
	.data-item {
		background-color: #f9fafb;
		padding: 24rpx;
		border-radius: 12rpx;
		text-align: center;
		
		.data-label {
			display: block;
			font-size: 22rpx;
			color: #6b7280;
			margin-bottom: 8rpx;
		}
		
		.data-value {
			display: block;
			font-size: 28rpx;
			font-weight: 600;
			color: #111827;
		}
	}
}

/* 设备按钮 */
.device-btn {
	background: linear-gradient(135deg, #3b82f6, #2563eb);
	border-radius: 12rpx;
	padding: 24rpx;
	text-align: center;
	margin: 24rpx 0;
	
	.device-btn-text {
		color: #ffffff;
		font-size: 28rpx;
		font-weight: 600;
	}
}

/* 验证区域 */
.verification-section {
	margin-top: 40rpx;
	
	.verification-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #111827;
		margin-bottom: 24rpx;
		display: block;
	}
	
	.verification-grid {
		display: grid;
		grid-template-columns: 1fr 1fr 1fr;
		gap: 16rpx;
		
		.verification-item {
			text-align: center;
			
			.verification-image {
				width: 100%;
				height: 200rpx;
				border-radius: 12rpx;
				margin-bottom: 8rpx;
				cursor: pointer;
				transition: transform 0.2s ease;

				&:active {
					transform: scale(0.95);
				}
			}
			
			.verification-label {
				font-size: 22rpx;
				color: #6b7280;
				display: block;
			}
		}
	}
}

/* 报告头部 */
.report-header {
	display: flex;
	align-items: center;
	margin-bottom: 16rpx;
	
	.lock-icon {
		font-size: 36rpx;
		margin-right: 12rpx;
	}
}

.report-meta {
	display: flex;
	justify-content: space-between;
	margin-bottom: 16rpx;
	padding-left: 48rpx;
	
	.report-time, .report-author {
		font-size: 22rpx;
		color: #6b7280;
	}
}

.report-content {
	padding-left: 48rpx;
	
	.report-text {
		font-size: 28rpx;
		color: #374151;
		line-height: 1.6;
	}
}

/* 放水记录头部 */
.water-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 24rpx;
	
	.water-title-group {
		display: flex;
		align-items: center;
		
		.water-icon {
			font-size: 36rpx;
			margin-right: 12rpx;
		}
	}
	
	.submit-clue-btn {
		background-color: #fef3c7;
		color: #d97706;
		padding: 8rpx 16rpx;
		border-radius: 24rpx;
		display: flex;
		align-items: center;
		
		.plus-icon {
			font-size: 20rpx;
			margin-right: 4rpx;
		}
		
		.submit-text {
			font-size: 22rpx;
			font-weight: 500;
		}
	}
}

/* 放水记录列表 */
.water-records {
	.water-record-item {
		display: flex;
		padding: 24rpx 0;
		
		&.border-bottom {
			border-bottom: 2rpx solid #f3f4f6;
		}
		
		.user-avatar {
			margin-right: 16rpx;
			
			.avatar-image {
				width: 64rpx;
				height: 64rpx;
				border-radius: 50%;
				background-color: #e5e7eb;
			}
		}
		
		.record-content {
			flex: 1;
			
			.record-header {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 8rpx;
				
				.user-info {
					display: flex;
					align-items: center;
					
					.user-name {
						font-size: 28rpx;
						font-weight: 500;
						color: #111827;
						margin-right: 8rpx;
					}
					
					.verify-status {
						font-size: 22rpx;
						
						&.verified {
							color: #16a34a;
						}
						
						&.unverified {
							color: #f59e0b;
						}
					}
				}
				
				.amount-tag {
					padding: 4rpx 12rpx;
					border-radius: 24rpx;
					
					.amount-text {
						font-size: 22rpx;
						font-weight: 500;
					}
					
					&.amount-high {
						background-color: #fee2e2;
						
						.amount-text {
							color: #dc2626;
						}
					}
					
					&.amount-medium {
						background-color: #fef3c7;
						
						.amount-text {
							color: #d97706;
						}
					}
					
					&.amount-low {
						background-color: #fef3c7;
						
						.amount-text {
							color: #d97706;
						}
					}
				}
			}
			
			.record-description {
				font-size: 28rpx;
				color: #374151;
				margin-bottom: 8rpx;
				line-height: 1.5;
			}
			
			.record-footer {
				display: flex;
				justify-content: space-between;
				margin-bottom: 16rpx;
				
				.device-info, .record-time {
					font-size: 22rpx;
					color: #6b7280;
				}
			}
			
			.screenshots {
				display: grid;
				grid-template-columns: 1fr 1fr;
				gap: 16rpx;
				
				.screenshot-image {
					width: 100%;
					height: 200rpx;
					border-radius: 12rpx;
					cursor: pointer;
					transition: transform 0.2s ease;

					&:active {
						transform: scale(0.95);
					}
				}
			}
		}
	}
}

/* 加载更多按钮 */
.load-more-container {
	text-align: center;
	margin-top: 32rpx;
	
	.load-more-btn {
		display: inline-flex;
		align-items: center;
		color: #ef4444;
		
		&.loading {
			color: #6b7280;
		}
		
		.load-more-text {
			font-size: 28rpx;
			font-weight: 500;
			margin-right: 8rpx;
		}
		
		.chevron-icon {
			font-size: 32rpx;
			font-weight: bold;
		}
	}
}

/* FAQ区域 */
.faq-header {
	display: flex;
	align-items: center;
	margin-bottom: 24rpx;
	
	.help-icon {
		font-size: 36rpx;
		margin-right: 12rpx;
	}
}

.faq-list {
	.faq-item {
		padding: 24rpx 0;
		
		&.border-bottom {
			border-bottom: 2rpx solid #f3f4f6;
		}
		
		.faq-question {
			font-size: 28rpx;
			font-weight: 500;
			color: #111827;
			margin-bottom: 16rpx;
			display: block;
		}
		
		.faq-answer {
			font-size: 24rpx;
			color: #6b7280;
			line-height: 1.6;
		}
	}
}

.more-faq-btn {
	display: flex;
	align-items: center;
	justify-content: center;
	margin-top: 24rpx;
	
	.more-faq-text {
		font-size: 28rpx;
		font-weight: 500;
		color: #ef4444;
		margin-right: 8rpx;
	}
	
	.chevron-icon {
		font-size: 32rpx;
		font-weight: bold;
		color: #ef4444;
	}
}

/* 底部按钮 */
.action-button-container {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	z-index: 998;
	background-color: #ffffff;
	border-top: 2rpx solid #f0f0f0;
	padding: 24rpx 32rpx;
	padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
	
	.action-button {
		background: linear-gradient(135deg, #3b82f6, #2563eb);
		border-radius: 12rpx;
		padding: 24rpx;
		text-align: center;
		box-shadow: 0 8rpx 24rpx rgba(59, 130, 246, 0.3);
		
		.action-button-text {
			color: #ffffff;
			font-size: 32rpx;
			font-weight: bold;
		}
	}
}

/* 底部安全区域 */
.safe-area-bottom {
	height: 120rpx;
}

/* 隐藏滚动条 */
::-webkit-scrollbar {
	display: none;
}
</style> 