<?php
declare(strict_types=1);

namespace App\Service\Report;

use App\Exceptions\MyException;
use App\Models\Citui\App;
use App\Models\Citui\EvaluationReport;
use App\Service\BaseService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class EvaluationReportService extends BaseService
{
    /**
     * 提交评测报告
     * 
     * @return array
     * @throws MyException
     */
    public function submitReport(): array
    {
        $data = $this->request->all();
        
        // 获取当前用户信息
        $user = $this->request->attributes->get('user');
        if (!$user) {
            throw new MyException('用户未登录');
        }

        if($user['is_admin'] != 1) {
            throw new MyException('您没有权限提交评测报告');
        }

        // 验证必填字段
        $this->validateFormData($data);

        DB::beginTransaction();
        try {
            // 1. 准备zc_apps表的数据
            $appData = $this->prepareAppData($data);
            
            // 2. 创建或更新APP记录
            $app = $this->createOrUpdateApp($appData);
            
            // 3. 准备zc_evaluation_reports表的数据
            $reportData = $this->prepareReportData($data, $app->id, $user['id']);
            
            // 4. 创建评测报告记录
            $report = $this->createEvaluationReport($reportData);
            
            DB::commit();
            
            return [
                'app_id' => $app->id,
                'report_id' => $report->id,
                'message' => '评测报告提交成功，审核通过后将获得50积分奖励'
            ];
            
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('评测报告提交失败: ' . $e->getMessage(), [
                'user_id' => $user['id'] ?? null,
                'data' => $data,
                'trace' => $e->getTraceAsString()
            ]);
            throw new MyException('提交失败，请重试');
        }
    }

    /**
     * 验证表单数据
     * 
     * @param array $data
     * @throws MyException
     */
    private function validateFormData(array $data): void
    {
        // 验证必填字段
        $requiredFields = [
            'app_name' => 'APP名称',
            'category_id' => 'APP类型',
            'logo_url' => 'APP Logo',
            'report_content' => '测评报告内容'
        ];

        foreach ($requiredFields as $field => $label) {
            if (empty($data[$field])) {
                throw new MyException($label . '不能为空');
            }
        }

        // 验证评分范围
        if (isset($data['pingfen']) && ($data['pingfen'] < 1 || $data['pingfen'] > 5)) {
            throw new MyException('评分必须在1-5之间');
        }

        // 验证运行模式
        if (isset($data['yunxingmoshi']) && !in_array($data['yunxingmoshi'], [1, 2])) {
            throw new MyException('运行模式参数错误');
        }
    }

    /**
     * 准备APP表数据
     * 
     * @param array $data
     * @return array
     */
    private function prepareAppData(array $data): array
    {
        return [
            'app_name' => $data['app_name'],
            'app_package' => $data['app_package'] ?? '',
            'app_version' => $data['app_version'] ?? '',
            'download_url' => $data['download_url'] ?? '',
            'logo_url' => $data['logo_url'] ?? '',
            'app_label' => $data['app_label'] ?? '',
            'category_id' => (int)$data['category_id'],
            'rating' => $this->formatDecimal($data['pingfen'] ?? 0),
            'download_count' => (int)($data['download_count'] ?? 0),
            'status' => 1,
            'created_at' => now(),
            'updated_at' => now()
        ];
    }

    /**
     * 创建或更新APP记录
     * 
     * @param array $appData
     * @return App
     */
    private function createOrUpdateApp(array $appData): App
    {
        $app = new App();
        
        // 检查是否已存在相同的APP（根据包名或名称）
        $existingApp = null;
        if (!empty($appData['app_package'])) {
            $existingApp = $app->where('app_package', $appData['app_package'])->first();
        }
        
        if (!$existingApp && !empty($appData['app_name'])) {
            $existingApp = $app->where('app_name', $appData['app_name'])->first();
        }

        // 调整为直接创建新记录
        return $app->create($appData);

        /* if ($existingApp) {
            // 更新现有记录
            unset($appData['created_at']);
            $existingApp->update($appData);
            return $existingApp;
        } else {
            // 创建新记录
            return $app->create($appData);
        } */
    }

    /**
     * 准备评测报告数据
     * 
     * @param array $data
     * @param int $appId
     * @param int $userId
     * @return array
     */
    private function prepareReportData(array $data, int $appId, int $userId): array
    {
        // 处理测试日期
        $cepingriqi = null;
        if (!empty($data['cepingriqi'])) {
            if (is_numeric($data['cepingriqi'])) {
                $cepingriqi = date('Y-m-d', (int)($data['cepingriqi'] / 1000));
            } else {
                $cepingriqi = date('Y-m-d', strtotime($data['cepingriqi']));
            }
        }

        return [
            'app_id' => $appId,
            'user_id' => $userId,
            'report_title' => $data['app_name'] . ' 评测报告',
            'report_content' => $data['report_content'] ?? '',
            'download_url' => $data['download_url'] ?? '',
            'pingfen' => (int)($data['pingfen'] ?? 4),
            'yunxingmoshi' => (int)($data['yunxingmoshi'] ?? 2),
            'xinrenfuli' => $this->formatDecimal($data['xinrenfuli'] ?? 0),
            'tixianmenkan' => $this->formatDecimal($data['tixianmenkan'] ?? 0),
            'dingbaojine' => $this->formatDecimal($data['dingbaojine'] ?? 0),
            'ceshitiaoshu' => (int)($data['ceshitiaoshu'] ?? 1),
            'ceshishouyi' => $this->formatDecimal($data['ceshishouyi'] ?? 0),
            'ceshishichang' => $this->formatDecimal($data['ceshishichang'] ?? 0),
            'ceshishebei' => $data['ceshishebei'] ?? '',
            'cepingren' => $data['cepingren'] ?? '',
            'cepingriqi' => $cepingriqi,
            'shouyi_1' => $this->formatDecimal($data['shouyi_1'] ?? 0),
            'shouyi_2' => $this->formatDecimal($data['shouyi_2'] ?? 0),
            'shouyi_3' => $this->formatDecimal($data['shouyi_3'] ?? 0),
            'shouyi_4' => $this->formatDecimal($data['shouyi_4'] ?? 0),
            'shouyi_5' => $this->formatDecimal($data['shouyi_5'] ?? 0),
            'pic_main' => $data['pic_main'] ?? '',
            'pic_tixian' => $data['pic_tixian'] ?? '',
            'pic_daozhang' => $data['pic_daozhang'] ?? '',
            'status' => 1, //默认通过审核
            'submitted_at' => now(),
            'created_at' => now(),
            'updated_at' => now()
        ];
    }

    /**
     * 创建评测报告记录
     * 
     * @param array $reportData
     * @return EvaluationReport
     */
    private function createEvaluationReport(array $reportData): EvaluationReport
    {
        $report = new EvaluationReport();
        return $report->create($reportData);
    }

    /**
     * 获取评测报告列表
     *
     * @return array
     */
    public function getEvaluationList(): array
    {
        // 获取当前用户信息（如果已登录）
        $currentUser = $this->request->attributes->get('user');
        $currentUserId = $currentUser['id'] ?? null;
        // 检查是否传递了用户ID筛选参数
        $filterUserId = $this->request->get('user_id');
        if (!empty($filterUserId)) {
            // 如果传递了用户ID，必须验证当前用户已登录
            if (!$currentUser) {
                throw new MyException('请先登录');
            }
            // 只能查询自己的数据
            if ($currentUserId != $filterUserId) {
                throw new MyException('只能查询自己的数据');
            }
        }

        // 构建查询
        $query = EvaluationReport::query()
            ->with(['app' => function($query) {
                $query->select('id', 'app_name','download_url', 'logo_url', 'category_id', 'rating', 'download_count', 'updated_at');
            }])
            ->where('status', 1); // 只显示审核通过的报告

        // 如果有用户ID筛选，添加条件
        if (!empty($filterUserId)) {
            $query->where('user_id', $filterUserId);
        }

        // 处理搜索条件
        $search = $this->request->get('search');
        if (!empty($search)) {
            $query->whereHas('app', function($q) use ($search) {
                $q->where('app_name', 'like', '%' . $search . '%');
            });
        }

        // 处理筛选条件
        $this->applyFilters($query);

        // 排序
        $query->orderBy('id', 'desc');

        // 获取总数
        $total = $query->count();

        // 获取分页数据
        $reports = $query->forPage($this->page_no, $this->per_page)->get();

        $evaluationList = $reports->map(function ($report) use ($currentUserId) {
            $app = $report->app;
            if (!$app) {
                return null; // 跳过没有关联APP的记录
            }

            // 判断是否是当前用户的数据
            $isOwner = 0;
            if ($currentUserId && $report->user_id == $currentUserId) {
                $isOwner = 1;
            }

            return [
                'id' => $report->id,
                'appId' => $app->id,
                'name' => $app->app_name,
                'type' => $this->getAppType($app->category_id),
                'download_url' => !empty($app->download_url) ? $app->download_url : '',
                'logo_url' => !empty($app->logo_url) ? uploadFilePathNoPre($app->logo_url) : '',
                'rating' => (float) $report->pingfen,
                'downloadCount' => $app->download_count,
                'updateTime' => $report->updated_at->format('m-d H:i'),
                'tags' => $this->generateEvaluationTags($report),
                'is_owner' => $isOwner, // 数据归属标志：0表示不是，1表示是
                // 评测相关数据
                'evaluation' => [
                    'xinrenfuli' => (float) $report->xinrenfuli,
                    'tixianmenkan' => (float) $report->tixianmenkan,
                    'yunxingmoshi' => $report->yunxingmoshi,
                    'ceshishouyi' => (float) $report->ceshishouyi,
                    'cepingriqi' => $report->cepingriqi ? $report->cepingriqi->format('Y-m-d') : null,
                ]
            ];
        })->filter()->values()->toArray(); // 过滤掉null值并重新索引

        return [
            'list' => $evaluationList,
            'page' => $this->page_no,
            'page_size' => $this->per_page,
            'total' => $total,
            'has_more' => ($this->page_no * $this->per_page) < $total
        ];
    }

    /**
     * 应用筛选条件
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     */
    private function applyFilters($query): void
    {
        // APP类型筛选
        $appTypes = $this->request->get('app_types');
        if (!empty($appTypes) && !in_array('全部', $appTypes)) {
            $categoryIds = $this->mapAppTypesToCategoryIds($appTypes);
            if (!empty($categoryIds)) {
                $query->whereHas('app', function($q) use ($categoryIds) {
                    $q->whereIn('category_id', $categoryIds);
                });
            }
        }

        // 运行模式筛选
        $runModes = $this->request->get('run_modes');
        if (!empty($runModes) && !in_array('全部', $runModes)) {
            $modeValues = [];
            foreach ($runModes as $mode) {
                if ($mode === '自动') {
                    $modeValues[] = 1;
                } elseif ($mode === '手动') {
                    $modeValues[] = 2;
                }
            }
            if (!empty($modeValues)) {
                $query->whereIn('yunxingmoshi', $modeValues);
            }
        }

        // 新人福利筛选
        $benefits = $this->request->get('benefits');
        if (!empty($benefits) && !in_array('全部', $benefits)) {
            foreach ($benefits as $benefit) {
                if ($benefit === '≥1元') {
                    $query->where('xinrenfuli', '>=', 1.00);
                } elseif ($benefit === '0.5-0.99元') {
                    $query->whereBetween('xinrenfuli', [0.50, 0.99]);
                } elseif ($benefit === '＜0.5元') {
                    $query->where('xinrenfuli', '<', 0.50);
                }
            }
        }

        // 提现门槛筛选
        $thresholds = $this->request->get('thresholds');
        if (!empty($thresholds) && !in_array('全部', $thresholds)) {
            foreach ($thresholds as $threshold) {
                if ($threshold === '≤0.1元') {
                    $query->where('tixianmenkan', '<=', 0.10);
                } elseif ($threshold === '0.1-1元') {
                    $query->whereBetween('tixianmenkan', [0.10, 1.00]);
                } elseif ($threshold === '＞1元') {
                    $query->where('tixianmenkan', '>', 1.00);
                }
            }
        }
    }

    /**
     * 将前端APP类型映射到数据库分类ID
     *
     * @param array $appTypes
     * @return array
     */
    private function mapAppTypesToCategoryIds(array $appTypes): array
    {
        $typeMap = [
            '合成游戏' => [1],
            '短剧' => [2],
            '阅读' => [3],
            '走路' => [4],
            '答题' => [5],
            '其它' => [6]
        ];

        $categoryIds = [];
        foreach ($appTypes as $type) {
            if (isset($typeMap[$type])) {
                $categoryIds = array_merge($categoryIds, $typeMap[$type]);
            }
        }

        return array_unique($categoryIds);
    }

    /**
     * 根据分类ID获取APP类型
     *
     * @param int $categoryId
     * @return string
     */
    private function getAppType(int $categoryId): string
    {
        $typeMap = [
            1 => 'coin',    // 合成游戏
            2 => 'video',   // 短剧
            3 => 'cash',    // 阅读
            4 => 'coin',    // 走路
            5 => 'cash',    // 答题
            6 => 'video',   // 其它
        ];

        return $typeMap[$categoryId] ?? 'coin';
    }

    /**
     * 生成评测标签
     *
     * @param EvaluationReport $report
     * @return array
     */
    private function generateEvaluationTags(EvaluationReport $report): array
    {
        $tags = [];

        // 运行模式标签
        if ($report->yunxingmoshi == 1) {
            $tags[] = ['text' => '自动', 'type' => 'red', 'emoji' => '🔥'];
        } else {
            $tags[] = ['text' => '手动', 'type' => 'blue', 'emoji' => ''];
        }

        // 提现门槛标签
        if ($report->tixianmenkan <= 0.1) {
            $tags[] = ['text' => '$0.1起提', 'type' => 'amber', 'emoji' => '⚡'];
        } elseif ($report->tixianmenkan <= 1.0) {
            $tags[] = ['text' => '$' . $report->tixianmenkan . '起提', 'type' => 'amber', 'emoji' => '⚡'];
        } else {
            $tags[] = ['text' => '$' . $report->tixianmenkan . '起提', 'type' => 'gray', 'emoji' => ''];
        }

        // 新人福利标签
        if ($report->xinrenfuli > 0) {
            $tags[] = ['text' => '新人$' . $report->xinrenfuli, 'type' => 'green', 'emoji' => ''];
        }

        // 顶包金额标签
        if ($report->dingbaojine > 0) {
            $tags[] = ['text' => '顶包￥' . $report->dingbaojine, 'type' => 'blue', 'emoji' => ''];
        }

        return array_slice($tags, 0, 4); // 最多显示4个标签
    }

    /**
     * 格式化小数
     *
     * @param mixed $value
     * @return float
     */
    private function formatDecimal($value): float
    {
        if (empty($value) || !is_numeric($value)) {
            return 0.00;
        }
        return round((float)$value, 2);
    }
}
