<?php
declare(strict_types=1);

namespace App\Service\App;

use App\Service\BaseService;
use App\Models\Citui\App;
use App\Models\Citui\AppCategory;
use App\Models\Citui\EvaluationReport;
use App\Exceptions\MyException;

class AppService extends BaseService
{
    /**
     * 获取首页数据
     * @return array
     */
    public function getHomeData(): array
    {
        // 获取推荐的APP列表
        $query = App::query()
            ->where('status', 1);

        // 如果有搜索关键词，添加搜索条件
        $search = $this->request->get('search');
        if (!empty($search)) {
            $query->where('app_name', 'like', '%' . $search . '%');
        }

        $query->orderBy('id', 'desc');

        // 获取总数
        $total = $query->count();

        // 如果数据库中没有数据，返回模拟数据
        if ($total == 0) {
            return [
                'list' => [],
                'page' => $this->page_no,
                'page_size' => $this->per_page,
                'total' => $total,
                'has_more' => false
            ];
        }

        // 获取分页数据
        $apps = $query->forPage($this->page_no, $this->per_page)->get();

        $appList = $apps->map(function ($app) {
            return [
                'id' => $app->id,
                'name' => $app->app_name,
                'type' => $this->getAppType($app->category_id),
                'logo_url' => !empty($app->logo_url) ? uploadFilePathNoPre($app->logo_url) : '',
                'download_url' => !empty($app->download_url) ? $app->download_url : '',
                'rating' => (float) $app->rating,
                'downloadCount' => $app->download_count,
                'updateTime' => $app->updated_at->format('m-d H:i'),
                'tags' => $this->generateTags($app)
            ];
        })->toArray();

        return [
            'list' => $appList,
            'page' => $this->page_no,
            'page_size' => $this->per_page,
            'total' => $total,
            'has_more' => ($this->page_no * $this->per_page) < $total
        ];
    }

    /**
     * 根据分类ID获取APP类型
     * @param int $categoryId
     * @return string
     */
    private function getAppType(int $categoryId): string
    {
        // 根据分类ID映射到前端需要的类型
        // 基于 zc_app_categories.sql 中的实际分类数据
        $typeMap = [
            1 => 'coin',    // 合成游戏 - 使用金币图标，适合游戏类应用
            2 => 'video',   // 短剧 - 使用视频图标，适合视频内容类应用
            3 => 'cash',    // 阅读 - 使用现金图标，适合阅读赚钱类应用
            4 => 'coin',    // 走路 - 使用金币图标，适合运动赚钱类应用
            5 => 'cash',    // 答题 - 使用现金图标，适合答题赚钱类应用
            6 => 'video',   // 其它 - 使用视频图标作为默认类型
        ];

        return $typeMap[$categoryId] ?? 'coin';
    }

    /**
     * 生成标签数据
     * @param App $app
     * @return array
     */
    private function generateTags(App $app): array
    {
        $tags = [];
        
        // 根据APP特性生成标签
        if ($app->app_label) {
            $labels = explode(',', $app->app_label);
            if ($labels) {
                foreach ($labels as $label) {
                    $tags[] = [
                        'text' => $label,
                        'type' => $this->getTagType($label),
                        'emoji' => $this->getTagEmoji($label)
                    ];
                }
            }
        }
        
        // 如果没有特性标签，生成默认标签
        if (empty($tags)) {
            return [];
        }
        
        return array_slice($tags, 0, 4); // 最多显示4个标签
    }

    /**
     * 获取标签类型
     * @param string $feature
     * @return string
     */
    private function getTagType(string $feature): string
    {
        $typeMap = [
            '热门' => 'red',
            '推荐' => 'red',
            '手动' => 'blue',
            '秒提现' => 'amber',
            '免费试用' => 'green',
            '免费体验' => 'green',
            '新人' => 'green',
            '顶包' => 'blue',
            '简单' => 'red',
            '走路赚钱' => 'amber'
        ];
        
        foreach ($typeMap as $keyword => $type) {
            if (strpos($feature, $keyword) !== false) {
                return $type;
            }
        }
        
        return 'gray';
    }

    /**
     * 获取标签emoji
     * @param string $feature
     * @return string
     */
    private function getTagEmoji(string $feature): string
    {
        $emojiMap = [
            '热门' => '🔥',
            '推荐' => '🔥',
            '秒提现' => '⚡',
            '手动' => '🔥',
            '简单' => '🔥',
            '走路赚钱' => '⚡'
        ];
        
        foreach ($emojiMap as $keyword => $emoji) {
            if (strpos($feature, $keyword) !== false) {
                return $emoji;
            }
        }
        
        return '';
    }

    /**
     * 获取默认标签
     * @param App $app
     * @return array
     */
    private function getDefaultTags(App $app): array
    {
        $defaultTags = [
            ['text' => '热门', 'type' => 'red', 'emoji' => '🔥'],
            ['text' => '秒提现', 'type' => 'amber', 'emoji' => '⚡'],
            ['text' => '免费试用', 'type' => 'green', 'emoji' => '']
        ];

        // 根据评分和下载量添加特殊标签
        if ($app->rating >= 4.5) {
            array_unshift($defaultTags, ['text' => '推荐', 'type' => 'red', 'emoji' => '🔥']);
        }

        if ($app->download_count >= 1000) {
            $defaultTags[] = ['text' => '￥0.1起提', 'type' => 'gray', 'emoji' => '⚡'];
        }

        return $defaultTags;
    }

    /**
     * 获取APP详情信息
     * @return array
     * @throws MyException
     */
    public function getAppDetail(): array
    {
        $appId = $this->request->get('id');
        if (empty($appId)) {
            throw new MyException('APP ID不能为空');
        }

        // 获取APP基本信息
        $app = App::with(['category', 'latestEvaluationReport'])
            ->where('id', $appId)
            ->where('status', 1)
            ->first();

        if (!$app) {
            throw new MyException('APP不存在或已下架');
        }

        // 获取评测报告信息
        $evaluationReport = $app->latestEvaluationReport;

        // 构建返回数据
        $result = [
            // APP基本信息
            'app_info' => [
                'id' => $app->id,
                'name' => $app->app_name,
                'type' => $this->getAppTypeName($app->category_id),
                'rating' => (float) $app->rating,
                'downloadCount' => $app->download_count,
                'logo' => uploadFilePathNoPre($app->logo_url),
                'tags' => $this->generateTags($app),
                'package' => $app->app_package ?? '',
                'version' => $app->app_version ?? '',
                'developer' => $app->developer ?? '',
                'description' => $app->description ?? '',
                'download_url' => $app->download_url ?? ''
            ],
            // 测试数据
            'test_data' => [],
            // 验证图片
            'verification_images' => [],
            // 报告信息
            'report_info' => []
        ];

        // 如果有评测报告，添加相关数据
        if ($evaluationReport) {
            $result['test_data'] = [
                'testCount' => $evaluationReport->ceshitiaoshu ?? 0,
                'testEarnings' => (float) ($evaluationReport->ceshishouyi ?? 0),
                'testDuration' => (float) ($evaluationReport->ceshishichang ?? 0),
                'testDevice' => $evaluationReport->ceshishebei ?? '',
                'xinrenfuli' => (float) ($evaluationReport->xinrenfuli ?? 0),
                'tixianmenkan' => (float) ($evaluationReport->tixianmenkan ?? 0),
                'dingbaojine' => (float) ($evaluationReport->dingbaojine ?? 0),
                'yunxingmoshi' => $evaluationReport->yunxingmoshi ?? 2,
                'yunxingmoshi_text' => $evaluationReport->yunxingmoshi == 1 ? '自动' : '手动'
            ];

            $result['verification_images'] = [
                [
                    'image' => uploadFilePathNoPre($evaluationReport->pic_main),
                    'label' => 'APP主界面'
                ],
                [
                    'image' => uploadFilePathNoPre($evaluationReport->pic_tixian),
                    'label' => 'APP内提现记录'
                ],
                [
                    'image' => uploadFilePathNoPre($evaluationReport->pic_daozhang),
                    'label' => '微信到账记录'
                ]
            ];

            $result['report_info'] = [
                'evaluationTime' => $evaluationReport->cepingriqi ? $evaluationReport->cepingriqi->format('Y-m-d') : '',
                'evaluator' => $evaluationReport->cepingren ?? '',
                'content' => $evaluationReport->report_content ?? '',
                'rating' => $evaluationReport->pingfen ?? 0
            ];
        }

        return $result;
    }

    /**
     * 根据分类ID获取APP类型名称
     * @param int $categoryId
     * @return string
     */
    private function getAppTypeName(int $categoryId): string
    {
        $typeMap = [
            1 => '合成游戏',
            2 => '短剧',
            3 => '阅读',
            4 => '走路',
            5 => '答题',
            6 => '其它',
        ];

        return $typeMap[$categoryId] ?? '其它';
    }

    /**
     * 获取APP列表（用于下拉选择）
     * @return array
     */
    public function getAppListForSelect(): array
    {
        $search = $this->request->get('search', '');

        $query = App::query()
            ->select(['id', 'app_name', 'category_id'])
            ->with(['category:category_id,category_name'])
            ->where('status', 1);

        // 支持按名称模糊搜索
        if (!empty($search)) {
            $query->where('app_name', 'like', '%' . $search . '%');
        }

        $query->orderBy('id', 'desc');

        $apps = $query->limit(500)->get();

        return $apps->map(function ($app) {
            return [
                'id' => $app->id,
                'name' => $app->app_name,
                'category_name' => $app->category->category_name ?? '未分类',
                'display_text' => $app->app_name
            ];
        })->toArray();
    }

}
